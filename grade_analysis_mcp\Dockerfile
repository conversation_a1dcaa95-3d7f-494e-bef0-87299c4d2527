# 基于Python 3.11 slim镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 安装Python数据科学库
RUN pip install --no-cache-dir \
    pandas==2.1.4 \
    numpy==1.24.3 \
    matplotlib==3.7.2 \
    seaborn==0.12.2 \
    scipy==1.11.4 \
    scikit-learn==1.3.2 \
    openpyxl==3.1.2 \
    xlrd==2.0.1

# 创建非特权用户
RUN useradd -m -u 1000 appuser
USER appuser

# 设置默认命令
CMD ["python"]
