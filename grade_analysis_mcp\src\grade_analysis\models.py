"""Grade Analysis MCP Server Data Models"""
import uuid
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class AnalysisRequest(BaseModel):
    """封装用户请求的分析信息"""
    user_request: str = Field(description="用户提出的个性化分析要求")
    data_string: str = Field(description="包含完整成绩数据的字符串，推荐使用Markdown表格格式")
    analysis_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="分析任务的唯一标识符"
    )


class AnalysisResult(BaseModel):
    """分析结果数据模型"""
    success: bool = Field(description="分析是否成功")
    analysis_id: str = Field(description="分析任务的唯一标识符")
    report: str = Field(description="Reporter-LLM生成的Markdown格式报告字符串")
    metadata: Dict[str, Any] = Field(description="包含分析统计信息、用量数据、生成时间等元数据")
    files: List[str] = Field(description="生成的文件列表（报告文件、数据文件等）")


class DataProcessingInfo(BaseModel):
    """数据预处理信息模型"""
    original_format: str = Field(description="原始数据格式")
    processed_format: str = Field(description="处理后的数据格式")
    rows_count: int = Field(description="数据行数")
    columns_count: int = Field(description="数据列数")
    columns_names: List[str] = Field(description="列名列表")
    processing_time: float = Field(description="处理耗时（秒）")
    file_path: str = Field(description="处理后的数据文件路径")


class CodeExecutionResult(BaseModel):
    """代码执行结果模型"""
    success: bool = Field(description="代码执行是否成功")
    stdout: str = Field(description="标准输出内容")
    stderr: str = Field(description="标准错误输出内容")
    execution_time: float = Field(description="执行耗时（秒）")
    return_code: int = Field(description="进程返回码")
    results: Optional[Dict[str, Any]] = Field(
        default=None,
        description="结构化的计算结果"
    )


class AIResponseInfo(BaseModel):
    """AI原始回复信息模型"""
    response_type: str = Field(description="回复类型：data_exploration, analysis, report")
    raw_response: str = Field(description="AI的原始回复内容")
    timestamp: str = Field(description="回复时间戳")
    file_path: str = Field(description="保存的文件路径")
    model_name: str = Field(description="使用的模型名称")
    tokens_used: int = Field(description="使用的Token数量")


class ModelUsageInfo(BaseModel):
    """记录单次模型调用的用量信息"""
    vendor: str = Field(description="提供模型的厂商，例如：dashscope, openai")
    model_name: str = Field(description="本次调用的具体模型名称")
    input_tokens: int = Field(description="输入给模型的Token数量")
    output_tokens: int = Field(description="模型生成的Token数量")


class ProgressUpdate(BaseModel):
    """进度更新消息模型"""
    status: str = Field(description="当前状态")
    message: str = Field(description="状态描述信息")
    progress: Optional[float] = Field(
        default=None,
        description="进度百分比（0-100）"
    )
    timestamp: str = Field(description="更新时间戳")
    details: Optional[Dict[str, Any]] = Field(
        default=None,
        description="额外的详细信息"
    )


class ErrorInfo(BaseModel):
    """错误信息模型"""
    error_type: str = Field(description="错误类型")
    error_message: str = Field(description="用户友好的错误描述")
    error_details: str = Field(description="详细的技术错误信息")
    stage: str = Field(description="发生错误的阶段")
    retry_suggested: bool = Field(description="是否建议重试")
    timestamp: str = Field(description="错误发生时间戳")
    analysis_id: Optional[str] = Field(
        default=None,
        description="相关的分析任务ID"
    )
