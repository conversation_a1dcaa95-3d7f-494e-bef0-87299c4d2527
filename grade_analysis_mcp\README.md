# AI成绩分析MCP服务器

这是一个基于 **MCP (Model Context Protocol)** 的 **AI成绩分析服务器**，专门用于智能分析学生成绩数据并生成专业的分析报告。

## 🎯 项目功能

- **智能数据解析**：自动解析Markdown格式的成绩表格数据
- **AI代码生成**：根据用户需求自动生成Python数据分析代码
- **安全代码执行**：基于Docker的安全沙箱环境执行分析代码
- **专业报告生成**：自动生成结构化的成绩分析报告
- **实时进度反馈**：提供分析过程的实时状态更新
- **MCP标准接口**：提供标准化的工具接口供AI助手调用

## 🏗️ 项目结构

```
grade-analysis-mcp/
├── README.md                   # 项目说明
├── pyproject.toml              # 项目配置
├── main.py                     # MCP服务器入口
├── .env.example                # 环境变量示例
├── Dockerfile                  # Docker镜像构建文件
├── src/                        # 核心源码目录
│   └── grade_analysis/
│       ├── __init__.py         # 模块初始化
│       ├── config.py           # 配置管理
│       ├── models.py           # 数据模型
│       ├── analyzer.py         # 核心分析逻辑
│       ├── ai_client.py        # AI服务客户端
│       ├── data_processor.py   # 数据处理器
│       ├── code_executor.py    # 代码执行器
│       └── logger.py           # 日志配置
├── tests/                      # 测试目录
│   ├── __init__.py
│   └── test_mcp_server.py      # 完整测试套件
├── logs/                       # 日志文件（运行时创建）
└── outputs/                    # 分析结果输出（运行时创建）
```

## 🏗️ 技术架构

### 核心组件

- **`main.py`** - MCP服务器主入口，提供`analyze_grades`工具函数
- **`src/grade_analysis/analyzer.py`** - 核心分析协调器
  - `GradeAnalyzer`: 主分析服务类，协调整个分析流程
- **`src/grade_analysis/ai_client.py`** - AI服务客户端
  - `DashScopeClient`: DashScope API客户端
  - `DataExplorer`: 数据探索代码生成
  - `DataAnalyst`: 数据分析代码生成
  - `ReportGenerator`: 分析报告生成
- **`src/grade_analysis/data_processor.py`** - 数据处理器
  - `DataProcessor`: Markdown表格解析和数据清理
- **`src/grade_analysis/code_executor.py`** - 安全代码执行器
  - `CodeExecutor`: 基于Docker的安全Python代码执行
- **`src/grade_analysis/models.py`** - 数据模型定义
- **`src/grade_analysis/config.py`** - 配置管理
- **`src/grade_analysis/logger.py`** - 日志系统配置

### 技术栈

- **框架**: FastMCP (MCP服务器框架)
- **AI服务**: DashScope API (阿里云)
  - 分析模型: `deepseek-v3`
- **数据处理**: Pandas
- **安全执行**: Docker
- **数据验证**: Pydantic
- **环境配置**: python-dotenv

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
cp .env.example .env
# 编辑 .env 文件，填入你的 DashScope API Key
```

### 2. Docker环境配置

```bash
# 构建分析环境镜像
docker build -t grade-analysis-python .

# 验证Docker环境
docker run --rm grade-analysis-python python -c "import pandas; print('Docker环境就绪')"
```

### 3. 配置选项

环境变量配置：
- `DASHSCOPE_API_KEY`: DashScope API密钥（必需）
- `DASHSCOPE_BASE_URL`: DashScope API基础URL（可选）
- `DASHSCOPE_MODEL`: 分析模型（可选，默认: deepseek-v3）
- `ENABLE_DOCKER`: 是否启用Docker安全执行（可选，默认: true）
- `DOCKER_IMAGE`: Docker镜像名称（可选，默认: grade-analysis-python）
- `OUTPUT_DIR`: 分析结果输出目录（可选，默认: outputs）

### 4. 运行服务

```bash
# 启动MCP服务器（HTTP流模式）
python main.py
# 服务器将在 http://localhost:8000/mcp/ 启动

# 运行测试
python tests/test_mcp_server.py
```

## 📚 使用示例

### HTTP API访问

服务器启动后，可通过HTTP接口访问：
- **服务地址**: `http://localhost:8000/mcp/`
- **传输模式**: HTTP Streamable
- **协议**: MCP (Model Context Protocol)

### MCP工具调用

```python
# 分析成绩数据
result = await analyze_grades(
    data_string="""| StudentID | StudentName | Class | Subject | Score |
|---|---|---|---|---|
| S001 | Student_1 | Class A | Math | 85 |
| S001 | Student_1 | Class A | English | 92 |
| S002 | Student_2 | Class A | Math | 78 |
| S002 | Student_2 | Class A | English | 88 |""",
    user_request="请分析各科目的平均分和最高分最低分",
    analysis_id="math_analysis_001"
)
```

### 参数说明

- `data_string`: Markdown格式的成绩表格数据（必需）
- `user_request`: 用户的分析需求描述（必需）
- `analysis_id`: 分析任务ID（可选，自动生成）

## 🎨 工作流程

项目的工作流程被设计为高度透明和实时，确保调用方能随时了解分析的进度。

1. **接收分析需求** - MCP工具`analyze_grades`接收成绩数据和分析需求
2. **启动实时通信** - 通过`fastmcp`的`Context`对象，开始向客户端进行实时状态推送
3. **数据处理** - 解析Markdown表格，清理和验证数据
4. **生成探索代码** - AI生成数据探索Python代码
5. **安全执行探索** - 在Docker容器中安全执行探索代码
6. **生成分析代码** - 基于探索结果，AI生成针对性分析代码
7. **安全执行分析** - 在Docker容器中安全执行分析代码
8. **生成分析报告** - AI基于分析结果生成专业报告
9. **输出最终结果** - 返回完整的分析结果和文件

## 📢 实时通知

在调用工具后，客户端可以通过监听服务端的实时消息来获取分析进度。所有的通知都通过`context.info()`以JSON格式发送。

关键的`status`类型包括：
- `data_processing`: 数据处理中
- `data_processed`: 数据处理完成
- `data_exploration`: 数据探索代码生成中
- `code_execution`: 代码执行中
- `exploration_completed`: 数据探索完成
- `analysis_execution`: 分析代码执行中
- `analysis_completed`: 数据分析完成
- `report_generation`: 报告生成中
- `completed`: 分析完成
- `model_usage`: 模型用量信息

## 📁 输出格式

### 生成内容

分析完成后会生成以下文件结构：
```
outputs/grade_analysis_HHMMSS_analysis-id/
├── data/
│   └── cleaned_grade_data.csv      # 清理后的数据文件
├── code/
│   ├── exploration_code.py         # 数据探索代码
│   └── analysis_code.py           # 数据分析代码
└── result/
    ├── analysis_report.md          # 分析报告
    └── metadata.json              # 元数据信息
```

### 返回结果

```json
{
    "success": true,
    "analysis_id": "math_analysis_001",
    "user_request": "请分析各科目的平均分和最高分最低分",
    "data_info": {
        "rows_count": 24,
        "columns_count": 5,
        "columns_names": ["StudentID", "StudentName", "Class", "Subject", "Score"],
        "processing_time": 0.15
    },
    "report_content": "# 学生成绩分析报告\n\n## 摘要\n...",
    "analysis_folder": "outputs/grade_analysis_180715_xxx",
    "total_time": 12.34,
    "model_usage_summary": {
        "total_calls": 3,
        "total_input_tokens": 1500,
        "total_output_tokens": 800
    },
    "files": ["..."]
}
```

## 🧪 测试

```bash
# 运行完整测试套件
python tests/test_mcp_server.py
```

测试套件包含：
- **数据处理测试**: 测试Markdown表格解析功能
- **代码执行测试**: 测试Docker安全执行环境
- **MCP服务器测试**: 测试MCP协议交互

## 🌟 特色功能

- **智能代码生成**: AI自动生成针对性的数据分析代码
- **安全沙箱执行**: Docker容器确保代码执行安全
- **实时进度反馈**: 完整的分析过程透明化
- **专业报告输出**: 结构化的分析报告，适合教师和管理者阅读
- **灵活数据格式**: 支持多种Markdown表格格式
- **高度定制**: 支持用户自定义分析需求

## 🔒 安全特性

- **Docker沙箱**: 所有代码在隔离的Docker容器中执行
- **资源限制**: 限制内存和CPU使用，防止资源滥用
- **网络隔离**: 执行环境无网络访问权限
- **只读文件系统**: 防止恶意文件操作
- **非特权用户**: 容器以nobody用户身份运行

## 📝 开发规范

本项目遵循 [MCP服务器开发规范](docs/mcp_rules.md)，包括：
- 实时反馈与流式输出
- 完整的日志记录
- 统一的错误处理
- 安全的密钥管理
- PEP 8代码规范
