"""Grade Analysis MCP Server Test Suite

测试MCP服务器的各项功能，包括：
1. 连接与调用测试
2. 成绩分析功能测试
3. 实时状态反馈测试
4. 错误处理测试
"""
import asyncio
import json
import pytest
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 测试数据
SAMPLE_GRADE_DATA = """
| 姓名 | 数学 | 语文 | 英语 | 物理 | 化学 |
|------|------|------|------|------|------|
| 张三 | 85   | 90   | 78   | 82   | 88   |
| 李四 | 92   | 88   | 85   | 90   | 85   |
| 王五 | 78   | 85   | 90   | 75   | 80   |
| 赵六 | 88   | 92   | 82   | 85   | 90   |
| 钱七 | 95   | 85   | 88   | 92   | 87   |
| 孙八 | 82   | 78   | 85   | 80   | 82   |
| 周九 | 90   | 88   | 92   | 88   | 85   |
| 吴十 | 87   | 90   | 80   | 85   | 88   |
"""

SAMPLE_USER_REQUEST = "请分析各科目的平均分、最高分、最低分，并找出总分排名前三的学生"


class TestGradeAnalysisMCP:
    """Grade Analysis MCP Server 测试类"""
    
    @pytest.fixture
    async def mcp_session(self):
        """创建MCP客户端会话"""
        server_params = StdioServerParameters(
            command="python",
            args=["main.py"],
            env=None
        )
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                yield session
    
    async def test_server_connection(self, mcp_session):
        """测试服务器连接"""
        # 获取可用工具列表
        tools = await mcp_session.list_tools()
        
        # 验证必要的工具存在
        tool_names = [tool.name for tool in tools.tools]
        assert "analyze_grades" in tool_names
        assert "test_environment" in tool_names
        
        print(f"可用工具: {tool_names}")
    
    async def test_environment_check(self, mcp_session):
        """测试环境检查功能"""
        # 调用环境测试工具
        result = await mcp_session.call_tool("test_environment", {})
        
        # 解析结果
        response_data = json.loads(result.content[0].text)
        
        # 验证基本结构
        assert "success" in response_data
        assert "components" in response_data
        assert "message" in response_data
        
        # 验证组件测试结果
        components = response_data["components"]
        assert "data_processor" in components
        assert "ai_client" in components
        assert "code_executor" in components
        
        print(f"环境测试结果: {response_data}")
    
    async def test_grade_analysis_basic(self, mcp_session):
        """测试基本的成绩分析功能"""
        # 准备测试参数
        params = {
            "grade_data": SAMPLE_GRADE_DATA,
            "user_request": SAMPLE_USER_REQUEST
        }
        
        # 调用分析工具
        result = await mcp_session.call_tool("analyze_grades", params)
        
        # 解析结果
        response_data = json.loads(result.content[0].text)
        
        # 验证基本结构
        assert "success" in response_data
        assert "analysis_id" in response_data
        assert "user_request" in response_data
        
        if response_data["success"]:
            # 验证成功响应的结构
            assert "data_info" in response_data
            assert "report_content" in response_data
            assert "analysis_folder" in response_data
            assert "total_time" in response_data
            assert "model_usage_summary" in response_data
            
            # 验证数据信息
            data_info = response_data["data_info"]
            assert data_info["rows_count"] == 8  # 8个学生
            assert data_info["columns_count"] == 6  # 姓名+5科
            assert "数学" in data_info["columns_names"]
            assert "语文" in data_info["columns_names"]
            
            # 验证报告内容不为空
            assert len(response_data["report_content"]) > 100
            
            print(f"分析成功: {response_data['analysis_id']}")
            print(f"报告长度: {len(response_data['report_content'])} 字符")
            print(f"总耗时: {response_data['total_time']:.2f} 秒")
        else:
            print(f"分析失败: {response_data.get('error', '未知错误')}")
            # 即使失败也要验证错误信息的结构
            assert "error" in response_data
    
    async def test_progress_feedback(self, mcp_session):
        """测试实时进度反馈"""
        progress_messages = []
        
        # 创建进度监听器
        def on_progress(notification):
            if notification.method == "notifications/progress":
                progress_messages.append(notification.params)
        
        # 注册进度监听器
        mcp_session.set_notification_handler(on_progress)
        
        # 执行分析
        params = {
            "grade_data": SAMPLE_GRADE_DATA,
            "user_request": "简单分析各科平均分"
        }
        
        result = await mcp_session.call_tool("analyze_grades", params)
        
        # 等待一段时间确保所有进度消息都被接收
        await asyncio.sleep(1)
        
        # 验证收到了进度消息
        assert len(progress_messages) > 0
        
        # 验证进度消息的结构
        for msg in progress_messages:
            assert "status" in msg
            print(f"进度消息: {msg}")
    
    async def test_error_handling(self, mcp_session):
        """测试错误处理"""
        # 测试无效的数据格式
        params = {
            "grade_data": "这不是一个有效的表格",
            "user_request": "分析成绩"
        }
        
        result = await mcp_session.call_tool("analyze_grades", params)
        response_data = json.loads(result.content[0].text)
        
        # 验证错误响应的结构
        assert "success" in response_data
        assert response_data["success"] is False
        assert "error" in response_data
        
        print(f"错误处理测试: {response_data['error']}")
    
    async def test_multiple_requests(self, mcp_session):
        """测试多个并发请求"""
        # 准备多个不同的分析请求
        requests = [
            {
                "grade_data": SAMPLE_GRADE_DATA,
                "user_request": "分析数学成绩分布"
            },
            {
                "grade_data": SAMPLE_GRADE_DATA,
                "user_request": "找出各科最优秀的学生"
            },
            {
                "grade_data": SAMPLE_GRADE_DATA,
                "user_request": "计算总分排名"
            }
        ]
        
        # 并发执行分析
        tasks = []
        for req in requests:
            task = mcp_session.call_tool("analyze_grades", req)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证所有请求都得到了响应
        assert len(results) == 3
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"请求 {i+1} 失败: {result}")
            else:
                response_data = json.loads(result.content[0].text)
                print(f"请求 {i+1} 状态: {response_data.get('success', False)}")


# 运行测试的主函数
async def main():
    """运行所有测试"""
    test_instance = TestGradeAnalysisMCP()
    
    print("开始测试 Grade Analysis MCP Server...")
    
    try:
        # 创建MCP会话
        server_params = StdioServerParameters(
            command="python",
            args=["main.py"],
            env=None
        )
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                await session.initialize()
                
                print("\n1. 测试服务器连接...")
                await test_instance.test_server_connection(session)
                
                print("\n2. 测试环境检查...")
                await test_instance.test_environment_check(session)
                
                print("\n3. 测试基本分析功能...")
                await test_instance.test_grade_analysis_basic(session)
                
                print("\n4. 测试错误处理...")
                await test_instance.test_error_handling(session)
                
                print("\n所有测试完成!")
                
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
