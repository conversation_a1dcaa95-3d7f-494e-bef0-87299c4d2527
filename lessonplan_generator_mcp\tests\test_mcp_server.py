#!/usr/bin/env python3
"""教案生成器MCP服务器简单测试脚本"""

import json
import requests
import sys

class SimpleMCPTester:
    """简单MCP测试器 - 只测试工具调用"""
    
    def __init__(self, server_url="http://127.0.0.1:8080"):
        self.server_url = server_url
        self.session = requests.Session()
    
    def test_generate_lesson_plan_tool(self):
        """测试教案生成工具"""
        print("🧪 教案生成器MCP工具测试")
        print("=" * 50)
        
        # 调用教案生成工具
        print("\n📚 步骤1: 调用教案生成工具...")
        
        try:
            # 构建JSON-RPC请求
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": "generate_lesson_plan",
                    "arguments": {
                        "topic": "光合作用",
                        "grade": "初中二年级",
                        "subject": "生物",
                        "teaching_hours": 1,
                        "user_requirements": "需要包含一个互动实验环节"
                    }
                }
            }
            
            print(f"   📦 请求负载: {json.dumps(payload, ensure_ascii=False, indent=2)}")
            
            headers = {"Content-Type": "application/json", "Accept": "application/json, text/event-stream"}
            
            print("🔄 正在发送请求...")
            print(f"   URL: {self.server_url}/mcp/")
            print(f"   参数: topic='光合作用', grade='初中二年级', subject='生物'")
            print("   ⏳ 教案生成可能需要一些时间，请耐心等待...")
            
            response = self.session.post(
                f"{self.server_url}/mcp/",
                json=payload,
                headers=headers,
                timeout=300,
                stream=True
            )
            
            print(f"\n📡 服务器响应 (流式):")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 请求成功！开始解析流式数据...")
                
                for line in response.iter_lines():
                    if not line:
                        continue
                    
                    line = line.decode('utf-8')
                    
                    if line.startswith('data:'):
                        json_data_str = line[len('data:'):].strip()
                        if not json_data_str:
                            continue
                            
                        try:
                            data = json.loads(json_data_str)
                            
                            if 'result' in data:
                                print("\n\n--- ✅ 任务完成 ---")
                                final_result_str = data['result']['content'][0]['text']
                                final_data = json.loads(final_result_str)
                                print("最终返回数据:")
                                print(json.dumps(final_data, ensure_ascii=False, indent=2))

                            elif 'error' in data:
                                error_info = data.get('error')
                                print(f"\n\n--- ❌ 任务失败 ---")
                                print(f"错误代码: {error_info.get('code')}")
                                print(f"错误信息: {error_info.get('message')}")

                            elif data.get('method') == 'notifications/message':
                                params = data.get('params', {})
                                if params.get('level') == 'info':
                                    progress_data_str = params.get('data', '{}')
                                    try:
                                        payload = json.loads(progress_data_str)
                                        status = payload.get('status')
                                        
                                        if status == 'lesson_plan_streaming':
                                            chunk = payload.get("chunk", "")
                                            print(chunk, end='', flush=True)
                                        elif status == 'model_usage':
                                            usage = payload.get('usage', {})
                                            print(f"\n[用量] "
                                                  f"厂商: {usage.get('vendor')}, "
                                                  f"模型: {usage.get('model_name')}, "
                                                  f"输入: {usage.get('input_tokens')}, "
                                                  f"输出: {usage.get('output_tokens')}")
                                        elif status:
                                            message = payload.get('message', '')
                                            print(f"\n[{status.upper()}] {message}")

                                    except json.JSONDecodeError:
                                        print(f"无法解析进度信息: {progress_data_str}")
                        except json.JSONDecodeError:
                            print(f"无法解析数据行: {json_data_str}")
                print("\n\n--- 🏁 流式响应结束 ---")
            else:
                print(f"   ❌ 请求失败: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"   ❌ 网络请求错误: {e}")
        except Exception as e:
            print(f"   ❌ 发生未知错误: {e}")

def main():
    """主函数，用于执行测试"""
    # 简单的命令行参数解析
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    else:
        server_url = "http://127.0.0.1:8080"
        
    print(f"🎯 测试目标服务器: {server_url}")
    
    tester = SimpleMCPTester(server_url)
    tester.test_generate_lesson_plan_tool()

if __name__ == "__main__":
    main() 