# AI教案生成MCP服务器

这是一个基于 **MCP (Model Context Protocol)** 的 **AI教案生成服务器**，专门用于根据用户需求自动生成完整的教案。

## 🎯 项目功能

- **智能教案设计**：根据主题、年级、学科等信息自动生成详细的教案内容。
- **多样化教学活动**：在教案中包含引入、新授、练习、总结等多个环节。
- **MCP标准接口**：提供标准化的工具接口供AI助手调用。

## 🏗️ 项目结构

```
lesson-plan-generator-mcp/
├── README.md                   # 项目说明
├── pyproject.toml              # 项目配置
├── main.py                     # MCP服务器入口
├── src/                        # 核心源码目录
│   └── lesson_plan_generator/
│       ├── __init__.py         # 模块初始化
│       ├── config.py           # 配置管理
│       ├── models.py           # 数据模型
│       ├── generators.py       # 核心生成逻辑
│       └── logger.py           # 日志配置
├── tests/                      # 测试目录
│   ├── __init__.py
│   └── test_mcp_server.py      # 完整测试套件
└── outputs/                    # 生成的教案输出（运行时创建）
```

## 🏗️ 技术架构

### 核心组件

- **`main.py`** - MCP服务器主入口，提供`generate_lesson_plan`工具函数
- **`src/lesson_plan_generator/generators.py`** - 核心教案生成模块
- **`src/lesson_plan_generator/models.py`** - 数据模型定义
- **`src/lesson_plan_generator/config.py`** - 配置管理
- **`src/lesson_plan_generator/logger.py`** - 日志系统配置

### 技术栈

- **框架**: FastMCP (MCP服务器框架)
- **AI服务**: 待定 (例如 ZhipuAI, Doubao, etc.)
- **数据验证**: Pydantic
- **环境配置**: python-dotenv

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export ZHIPUAI_API_KEY="your_zhipuai_api_key"
```

### 2. 运行服务

```bash
# 启动MCP服务器（HTTP流模式）
python main.py
# 服务器将在 http://localhost:8080 启动

# 运行测试
python tests/test_mcp_server.py
```

## 📚 使用示例

### MCP工具调用

```python
# 生成教案
result = await generate_lesson_plan(
    topic="光合作用",
    grade="初中二年级",
    subject="生物",
    teaching_hours=1,
    user_requirements="需要包含一个互动实验环节"
)
```

### 参数说明

- `topic`: 教案主题
- `grade`: 授课年级
- `subject`: 所属学科
- `teaching_hours`: 计划课时
- `user_requirements`: 用户特殊要求（可选） 