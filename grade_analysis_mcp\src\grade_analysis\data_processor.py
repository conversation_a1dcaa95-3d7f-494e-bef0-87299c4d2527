"""Grade Analysis Data Processor Module

实现Markdown表格到CSV的数据预处理功能，包括数据清理、文件管理和AI原始回复保存。
"""
import os
import re
import time
import uuid
from datetime import datetime
from typing import Tuple, List, Dict, Any
import pandas as pd
from tabulate import tabulate

from .models import DataProcessingInfo, AIResponseInfo
from .logger import get_logger

logger = get_logger(__name__)


class DataProcessor:
    """数据预处理器类
    
    负责处理用户输入的Markdown表格数据，转换为标准CSV格式，
    并管理分析任务的文件存储结构。
    """
    
    def __init__(self, output_dir: str = "outputs"):
        """初始化数据处理器
        
        Args:
            output_dir (str): 输出目录根路径
        """
        self.output_dir = output_dir
        self.analysis_folder = None
        
    def parse_markdown_table(self, markdown_data: str) -> pd.DataFrame:
        """解析Markdown表格格式数据
        
        Args:
            markdown_data (str): Markdown格式的表格数据
            
        Returns:
            pd.DataFrame: 解析后的数据框
            
        Raises:
            ValueError: 如果数据格式不正确
        """
        logger.info("开始解析Markdown表格数据")
        start_time = time.time()
        
        try:
            # 按行分割数据
            lines = markdown_data.strip().split('\n')
            
            # 过滤空行和只包含空白字符的行
            lines = [line.strip() for line in lines if line.strip()]
            
            if len(lines) < 2:
                raise ValueError("Markdown表格数据至少需要包含表头和分隔符行")
            
            # 解析表头
            header_line = lines[0]
            if not header_line.startswith('|') or not header_line.endswith('|'):
                raise ValueError("表头行格式不正确，应以'|'开始和结束")
            
            # 提取列名
            headers = [col.strip() for col in header_line.split('|')[1:-1]]
            logger.info(f"检测到列名: {headers}")
            
            # 验证分隔符行
            if len(lines) < 2:
                raise ValueError("缺少表格分隔符行")
            
            separator_line = lines[1]
            if not re.match(r'^\|[\s\-\|]+\|$', separator_line):
                logger.warning("分隔符行格式可能不标准，尝试继续解析")
            
            # 解析数据行
            data_rows = []
            for i, line in enumerate(lines[2:], start=3):
                if not line.startswith('|') or not line.endswith('|'):
                    logger.warning(f"第{i}行格式不正确，跳过: {line}")
                    continue
                
                # 提取单元格数据
                cells = [cell.strip() for cell in line.split('|')[1:-1]]
                
                # 确保列数匹配
                if len(cells) != len(headers):
                    logger.warning(f"第{i}行列数不匹配，期望{len(headers)}列，实际{len(cells)}列")
                    # 补齐或截断
                    if len(cells) < len(headers):
                        cells.extend([''] * (len(headers) - len(cells)))
                    else:
                        cells = cells[:len(headers)]
                
                data_rows.append(cells)
            
            if not data_rows:
                raise ValueError("没有找到有效的数据行")
            
            # 创建DataFrame
            df = pd.DataFrame(data_rows, columns=headers)
            
            processing_time = time.time() - start_time
            logger.info(f"Markdown表格解析完成，耗时{processing_time:.2f}秒")
            logger.info(f"数据形状: {df.shape}")
            
            return df
            
        except Exception as e:
            logger.error(f"解析Markdown表格失败: {str(e)}")
            raise ValueError(f"Markdown表格解析失败: {str(e)}")
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和验证数据
        
        Args:
            df (pd.DataFrame): 原始数据框
            
        Returns:
            pd.DataFrame: 清理后的数据框
        """
        logger.info("开始数据清理和验证")
        
        # 记录原始数据信息
        original_shape = df.shape
        logger.info(f"原始数据形状: {original_shape}")
        
        # 创建副本避免修改原始数据
        cleaned_df = df.copy()
        
        # 处理空值
        null_counts = cleaned_df.isnull().sum()
        if null_counts.any():
            logger.info(f"发现空值: {null_counts.to_dict()}")
            # 对于数值列，可以考虑填充0或平均值
            # 这里先保持空值，让后续分析代码处理
        
        # 尝试转换数值列
        for col in cleaned_df.columns:
            if col.lower() in ['姓名', '学生姓名', '班级', '学生id', 'id', 'studentid', 'studentname', 'class', 'name', 'subject', 'examdate', 'date']:
                # 跳过明显的文本列
                continue
            
            # 尝试转换为数值
            try:
                # 先清理可能的非数值字符
                cleaned_series = cleaned_df[col].astype(str).str.replace(r'[^\d\.\-]', '', regex=True)
                numeric_series = pd.to_numeric(cleaned_series, errors='coerce')
                
                # 如果转换成功的比例较高，则认为是数值列
                valid_ratio = numeric_series.notna().sum() / len(numeric_series)
                if valid_ratio > 0.7:  # 70%以上可以转换为数值
                    cleaned_df[col] = numeric_series
                    logger.info(f"列'{col}'转换为数值类型，有效率: {valid_ratio:.2%}")
                    
            except Exception as e:
                logger.debug(f"列'{col}'数值转换失败: {str(e)}")
        
        # 移除完全空的行
        before_rows = len(cleaned_df)
        cleaned_df = cleaned_df.dropna(how='all')
        after_rows = len(cleaned_df)
        
        if before_rows != after_rows:
            logger.info(f"移除了{before_rows - after_rows}个完全空的行")
        
        logger.info(f"数据清理完成，最终形状: {cleaned_df.shape}")
        return cleaned_df
    
    def create_analysis_folder(self, analysis_id: str = None) -> str:
        """创建客户端输出文件夹

        Args:
            analysis_id (str, optional): 分析任务ID

        Returns:
            str: 创建的文件夹路径
        """
        if analysis_id is None:
            analysis_id = str(uuid.uuid4())

        timestamp = datetime.now().strftime("%H%M%S")
        folder_name = f"grade_analysis_{timestamp}_{analysis_id}"

        self.analysis_folder = os.path.join(self.output_dir, folder_name)

        # 创建主文件夹和子文件夹
        subfolders = ['data', 'code', 'result']
        for subfolder in subfolders:
            os.makedirs(os.path.join(self.analysis_folder, subfolder), exist_ok=True)

        logger.info(f"创建客户端输出文件夹: {self.analysis_folder}")
        return self.analysis_folder
    
    def save_csv_data(self, df: pd.DataFrame, folder_path: str) -> str:
        """保存清理后的数据为CSV文件
        
        Args:
            df (pd.DataFrame): 要保存的数据框
            folder_path (str): 文件夹路径
            
        Returns:
            str: 保存的CSV文件路径
        """
        csv_path = os.path.join(folder_path, 'data', 'cleaned_grade_data.csv')
        
        try:
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            logger.info(f"数据已保存到: {csv_path}")
            return csv_path
        except Exception as e:
            logger.error(f"保存CSV文件失败: {str(e)}")
            raise
    

    def process_data(
        self, 
        markdown_data: str, 
        analysis_id: str = None
    ) -> Tuple[pd.DataFrame, DataProcessingInfo, str]:
        """完整的数据处理流程
        
        Args:
            markdown_data (str): Markdown格式的表格数据
            analysis_id (str, optional): 分析任务ID
            
        Returns:
            Tuple[pd.DataFrame, DataProcessingInfo, str]: 
                处理后的数据框、处理信息、文件夹路径
        """
        start_time = time.time()
        
        # 解析Markdown表格
        df = self.parse_markdown_table(markdown_data)
        
        # 清理数据
        cleaned_df = self.clean_data(df)
        
        # 创建分析文件夹
        folder_path = self.create_analysis_folder(analysis_id)
        
        # 保存CSV数据
        csv_path = self.save_csv_data(cleaned_df, folder_path)
        
        # 创建处理信息
        processing_time = time.time() - start_time
        processing_info = DataProcessingInfo(
            original_format="markdown_table",
            processed_format="csv",
            rows_count=len(cleaned_df),
            columns_count=len(cleaned_df.columns),
            columns_names=cleaned_df.columns.tolist(),
            processing_time=processing_time,
            file_path=csv_path
        )
        
        logger.info(f"数据处理完成，总耗时: {processing_time:.2f}秒")
        
        return cleaned_df, processing_info, folder_path
