"""AI教案生成核心逻辑模块"""
import json
from typing import Dict, Any, Optional, Tuple

from fastmcp import Context
from zhipuai import ZhipuAI

from .config import LessonPlanGeneratorConfig
from .logger import get_logger
from .models import LessonPlanInfo, ModelUsageInfo

class BaseZhipuAgent:
    """封装与智谱AI平台交互的通用逻辑"""
    
    def __init__(self, config: LessonPlanGeneratorConfig):
        """初始化智谱AI Agent"""
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.client = None
        self.init_client()
    
    def init_client(self):
        """根据配置初始化智谱AI的API客户端"""
        try:
            self.client = ZhipuAI(
                api_key=self.config.zhipuai_api_key,
                base_url=self.config.zhipuai_base_url
            )
            self.logger.info("智谱AI模型客户端初始化成功")
        except Exception as e:
            self.logger.error(f"初始化智谱AI模型客户端失败: {str(e)}")
            raise

class LessonPlanGenerationAgent(BaseZhipuAgent):
    """负责生成教案内容的智能体"""
    
    async def generate_lesson_plan_stream(
        self, plan_info: LessonPlanInfo, context: Optional[Context] = None
    ) -> Tuple[str, ModelUsageInfo]:
        """
        调用语言大模型，以流式方式生成教案。
        同时，将生成的文本块通过context实时发送给客户端，并返回最终的完整教案和用量信息。
        """
        if not self.client:
            raise ValueError("智谱AI客户端未初始化")
        
        self.logger.info(
            f"开始流式生成教案: {plan_info.topic} ({plan_info.grade} {plan_info.subject})"
        )
        
        prompt = f"""
请根据以下信息，为我生成一份详细的教案：

# 教案信息
- **主题**: {plan_info.topic}
- **年级**: {plan_info.grade}
- **学科**: {plan_info.subject}
- **课时**: {plan_info.teaching_hours}
- **用户特殊要求**: {plan_info.user_requirements or "无"}

# 教案结构要求
请严格按照以下结构生成，确保内容完整、逻辑清晰：
1. **教学目标**:
   - 知识与技能
   - 过程与方法
   - 情感态度与价值观
2. **教学重难点**:
   - 重点
   - 难点
3. **教学准备**:
   - 教师准备
   - 学生准备
4. **教学过程**:
   - **环节一：导入 (约5分钟)**: [设计一个有趣的课堂导入活动]
   - **环节二：新知探究 (约25分钟)**: [详细讲解本课的核心知识点，可以分步进行]
   - **环节三：巩固练习 (约10分钟)**: [设计1-2个练习题或小组活动]
   - **环节四：课堂小结 (约5分钟)**: [引导学生总结本节课的收获]
5. **板书设计**: [设计一个清晰、有条理的板书]
6. **作业布置**: [分层设计作业，满足不同学生需求]
"""
        
        try:
            self.logger.debug(f"调用教案生成流式API，模型: {self.config.zhipuai_model}")
            response_stream = self.client.chat.completions.create(
                model=self.config.zhipuai_model,
                messages=[
                    {
                        "role": "system", 
                        "content": "你是一位资深的教学设计专家，精通各个学科的课程设计和教案撰写。"
                    },
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                stream=True,
            )
            
            full_content = ""
            final_usage = None
            for chunk in response_stream:
                content_piece = chunk.choices[0].delta.content
                if content_piece:
                    if context:
                        await context.info(json.dumps({
                            "status": "lesson_plan_streaming",
                            "chunk": content_piece
                        }))
                    full_content += content_piece
                
                if chunk.usage:
                    final_usage = chunk.usage
            
            self.logger.info("教案流式生成完成")

            if not final_usage:
                self.logger.warning("未能从API响应中获取用量信息，将估算一个默认值。")
                # 如果没有用量信息，提供一个默认值或进行估算
                final_usage = {"prompt_tokens": 0, "completion_tokens": 0}


            usage_info = ModelUsageInfo(
                vendor="zhipuai",
                model_name=self.config.zhipuai_model,
                input_tokens=final_usage.prompt_tokens,
                output_tokens=final_usage.completion_tokens
            )
            return full_content, usage_info
            
        except Exception as e:
            self.logger.error(f"生成教案失败: {str(e)}")
            raise ValueError(f"生成教案失败: {str(e)}")

class LessonPlanGeneratorService:
    """负责统筹和执行教案生成任务的主服务"""
    
    def __init__(self, config: LessonPlanGeneratorConfig):
        """初始化服务和其依赖的Agent"""
        self.config = config
        self.logger = get_logger(self.__class__.__name__)
        self.generation_agent = LessonPlanGenerationAgent(config)
        self.logger.info("教案生成服务已创建")
    
    async def generate_lesson_plan(
        self, plan_info: LessonPlanInfo, context: Context
    ) -> Dict[str, Any]:
        """
        生成一份完整的教案，并通过MCP上下文实时报告进度。
        """
        async def send_progress(status: str, message: str, extra: Optional[Dict] = None):
            payload = {"status": status, "message": message}
            if extra:
                payload.update(extra)
            await context.info(json.dumps(payload, ensure_ascii=False))

        try:
            await send_progress(
                "generation_started", f"已接收请求，开始生成《{plan_info.topic}》教案"
            )

            # 1. 生成教案内容
            lesson_plan_content, usage_info = await self.generation_agent.generate_lesson_plan_stream(
                plan_info, context
            )
            
            await send_progress(
                "model_usage", "模型调用完成", 
                {"usage": usage_info.model_dump()}
            )
            await send_progress("generation_completed", "教案内容生成完毕")
            
            # 2. 准备最终结果
            result = {
                "success": True,
                "message": "教案生成成功",
                "lesson_plan": {
                    "info": plan_info.model_dump(),
                    "content": lesson_plan_content,
                },
                "usage": usage_info.model_dump()
            }
            
            return result

        except Exception as e:
            self.logger.exception(f"在生成教案《{plan_info.topic}》时发生严重错误")
            error_message = f"生成教案时出错: {str(e)}"
            await send_progress("error", error_message)
            return {
                "success": False,
                "message": error_message
            } 