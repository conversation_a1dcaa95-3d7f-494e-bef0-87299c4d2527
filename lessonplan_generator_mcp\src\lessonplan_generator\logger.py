"""教案生成器日志配置模块

提供统一的日志配置和管理功能，支持文件输出、控制台输出和不同日志级别。
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from typing import Optional


class LessonPlanLogger:
    """
    一个集中的日志管理类，用于提供统一的、可配置的日志记录功能。
    
    该类使用单例模式（通过_initialized标志）确保全局日志配置只被设置一次。
    它支持将日志同时输出到控制台和多个文件（按级别和日期分割），并能自动轮转。
    """
    
    _loggers = {}
    _initialized = False
    
    @classmethod
    def setup_logging(
        cls,
        log_dir: str = "logs",
        log_level: str = "INFO",
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5,
        console_output: bool = True
    ) -> None:
        """
        设置或重新配置全局日志系统。
        
        该方法会配置根日志器(root logger)，并为其添加多个处理器：
        1. 一个用于记录所有级别日志的轮转文件处理器。
        2. 一个专门用于记录ERROR及以上级别日志的轮转文件处理器。
        3. 一个用于在控制台实时显示日志的流处理器。

        Args:
            log_dir (str, optional): 日志文件存放的目录. Defaults to "logs".
            log_level (str, optional): 应用的全局日志级别. Defaults to "INFO".
            max_file_size (int, optional): 单个日志文件的最大体积（字节）. Defaults to 10MB.
            backup_count (int, optional): 保留的旧日志文件数量. Defaults to 5.
            console_output (bool, optional): 是否将日志输出到标准输出. Defaults to True.
        """
        if cls._initialized:
            # 防止重复初始化，确保配置只应用一次
            return
            
        # 创建日志目录
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置根日志级别
        numeric_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # 创建格式化器
        detailed_formatter = logging.Formatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - '
                '%(filename)s:%(lineno)d - %(funcName)s() - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        simple_formatter = logging.Formatter(
            fmt='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(numeric_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 添加文件处理器 - 详细日志
        log_filename = os.path.join(
            log_dir, 
            f"lessonplan_generator_{datetime.now().strftime('%Y%m%d')}.log"
        )
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_filename,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(file_handler)
        
        # 添加错误日志文件处理器
        error_log_filename = os.path.join(
            log_dir,
            f"lessonplan_generator_error_{datetime.now().strftime('%Y%m%d')}.log"
        )
        error_file_handler = logging.handlers.RotatingFileHandler(
            filename=error_log_filename,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_file_handler.setLevel(logging.ERROR)
        error_file_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(error_file_handler)
        
        # 添加控制台处理器（如果启用）
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(numeric_level)
            console_handler.setFormatter(simple_formatter)
            root_logger.addHandler(console_handler)
        
        cls._initialized = True
        
        # 使用配置好的日志器记录初始化信息
        logger = cls.get_logger("LessonPlanLogger")
        logger.info("日志系统初始化完成")
        logger.info(f"日志目录: {log_dir}")
        logger.info(f"日志级别: {log_level}")
        logger.info(f"控制台输出: {console_output}")
    
    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """
        获取一个指定名称的日志器实例。
        
        该方法利用缓存(_loggers)避免重复创建日志器实例。

        Args:
            name (str): 日志器的名称，通常使用 __name__。
            
        Returns:
            logging.Logger: 一个配置好的日志器实例。
        """
        if name not in cls._loggers:
            logger = logging.getLogger(name)
            cls._loggers[name] = logger
        
        return cls._loggers[name]
    
    @classmethod
    def set_level(cls, name: str, level: str) -> None:
        """
        在运行时动态设置指定日志器的日志级别。

        Args:
            name (str): 要修改的日志器的名称。
            level (str): 新的日志级别字符串 (e.g., "DEBUG", "WARNING")。
        """
        if name in cls._loggers:
            numeric_level = getattr(logging, level.upper(), logging.INFO)
            cls._loggers[name].setLevel(numeric_level)


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取日志器的便捷函数。
    
    如果未提供名称，它会自动检查调用栈，使用调用方的模块名作为日志器名称，
    这简化了在其他模块中获取日志器的代码。

    Args:
        name (Optional[str], optional): 日志器名称. Defaults to None.
        
    Returns:
        logging.Logger: 日志器实例。
    """
    if name is None:
        # 自动获取调用者的模块名
        import inspect
        frame = inspect.currentframe()
        if frame and frame.f_back:
            name = frame.f_back.f_globals.get('__name__', 'unknown')
        else:
            name = 'unknown'
    
    return LessonPlanLogger.get_logger(name)


# 初始化默认日志配置
def init_default_logging() -> None:
    """
    在模块首次加载时，执行一次默认的日志初始化。
    这样可以确保即使在主程序未明确配置日志时，也能有基本的日志功能。
    最终配置会由主程序中的 `LessonPlanLogger.setup_logging` 调用覆盖。
    """
    # 获取项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(current_dir))
    log_dir = os.path.join(project_root, "logs")
    
    # 从环境变量获取日志级别
    log_level = os.getenv("LOG_LEVEL", "INFO")
    console_output = os.getenv("LOG_CONSOLE", "true").lower() == "true"
    
    LessonPlanLogger.setup_logging(
        log_dir=log_dir,
        log_level=log_level,
        console_output=console_output
    )


# 在模块加载时初始化日志
if not LessonPlanLogger._initialized:
    init_default_logging() 