"""AI教案生成MCP服务器主文件"""
import json
import sys

from fastmcp import FastMCP, Context

from src.lesson_plan_generator.config import LessonPlanGeneratorConfig
from src.lesson_plan_generator.logger import get_logger, LessonPlanLogger
from src.lesson_plan_generator.models import LessonPlanInfo
from src.lesson_plan_generator.generators import LessonPlanGeneratorService

# 初始化MCP服务器
mcp = FastMCP("Lesson Plan Generator MCP Server")

# 全局配置和服务实例
config = None
plan_service = None
logger = get_logger(__name__)


def initialize_service():
    """初始化服务"""
    global config, plan_service
    try:
        config = LessonPlanGeneratorConfig.from_env()
        
        # 使用配置中的日志设置重新初始化日志系统
        LessonPlanLogger.setup_logging(
            log_dir=config.log_dir,
            log_level=config.log_level,
            console_output=config.log_console
        )
        
        plan_service = LessonPlanGeneratorService(config)
        logger.info("教案生成服务初始化成功")
    except Exception as e:
        logger.error(f"服务初始化失败: {str(e)}")
        raise

@mcp.tool()
async def generate_lesson_plan(
    topic: str,
    grade: str,
    subject: str,
    ctx: Context,
    teaching_hours: int = 1,
    user_requirements: str = ""
) -> str:
    """根据教案信息生成完整的教案

    Args:
        topic (str): 教案主题.
        grade (str): 授课年级.
        subject (str): 所属学科.
        ctx (Context): MCP上下文，由框架自动注入.
        teaching_hours (int, optional): 计划课时. Defaults to 1.
        user_requirements (str, optional): 用户特殊要求. Defaults to "".

    Returns:
        str: JSON格式的最终生成结果.
    """
    global plan_service

    if not plan_service:
        error_msg = "服务未初始化"
        logger.error(error_msg)
        return json.dumps({
            "success": False,
            "message": error_msg,
            "error": "Lesson plan generation service not initialized"
        }, ensure_ascii=False)

    try:
        logger.info(
            f"开始生成教案: 主题='{topic}', 年级='{grade}', "
            f"学科={subject}"
        )
        
        # 创建教案信息对象
        plan_info = LessonPlanInfo(
            topic=topic,
            grade=grade,
            subject=subject,
            teaching_hours=teaching_hours,
            user_requirements=user_requirements
        )

        # 生成教案，并传入请求上下文
        result = await plan_service.generate_lesson_plan(plan_info, ctx)
        
        if result.get("success", False):
            logger.info(f"教案生成成功: {result.get('message', '')}")
        else:
            logger.error(f"教案生成失败: {result.get('message', '')}")

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_msg = f"教案生成过程中发生错误: {str(e)}"
        logger.exception(error_msg)
        return json.dumps({
            "success": False,
            "message": "教案生成过程中发生错误",
            "error": str(e)
        }, ensure_ascii=False)

if __name__ == "__main__":
    try:
        initialize_service()
        logger.info("启动教案生成器MCP服务器...")
        mcp.run(transport='streamable-http')
    except Exception as e:
        logger.critical(f"服务器启动失败: {str(e)}")
        sys.exit(1) 