# DashScope AI 服务配置
DASHSCOPE_API_KEY=api_key
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
DASHSCOPE_MODEL=deepseek-v3

# 分析参数配置
MAX_TOKENS=4000
TEMPERATURE=0.1
REQUEST_TIMEOUT=60

# 安全执行配置
ENABLE_DOCKER=true
DOCKER_IMAGE=grade-analysis-python
EXECUTION_TIMEOUT=30
MAX_MEMORY=512m
MAX_CPU=1.0

# 输出配置
OUTPUT_DIR=outputs
CLIENT_OUTPUT_DIR=outputs
REPORT_FORMAT=markdown

# 日志配置
LOG_DIR=logs
LOG_LEVEL=INFO
LOG_CONSOLE=true
