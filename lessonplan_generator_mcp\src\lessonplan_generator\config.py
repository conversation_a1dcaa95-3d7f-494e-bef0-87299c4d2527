"""AI教案生成服务器配置模块"""
import os
from typing import Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# 在应用启动时加载.env文件中的环境变量
load_dotenv()


class LessonPlanGeneratorConfig(BaseModel):
    """
    应用配置类，集中管理所有可配置项。
    配置值优先从环境变量中读取，若环境变量未设置，则使用代码中定义的默认值。
    """
    
    # --- 智谱AI API配置 ---
    zhipuai_api_key: str = Field(description="智谱AI平台的API访问密钥")
    zhipuai_base_url: Optional[str] = Field(
        default=None,
        description="智谱AI API服务的基地址 (可选)"
    )
    zhipuai_model: str = Field(
        default="glm-4",
        description="用于生成教案的语言大模型名称"
    )
    
    # --- 输出路径配置 ---
    output_dir: str = Field(default="outputs", description="生成的教案文件存放的根目录")
    
    # --- 日志系统配置 ---
    log_dir: str = Field(default="logs", description="日志文件的存放目录")
    log_level: str = Field(default="INFO", description="日志记录的最低级别")
    log_console: bool = Field(default=True, description="是否同时将日志输出到控制台")
    
    @classmethod
    def from_env(cls) -> "LessonPlanGeneratorConfig":
        """
        从环境变量构造配置实例。
        
        该方法会逐一检查每个配置项对应的环境变量，如果存在则使用环境变量的值，
        否则使用在类中定义的默认值。对于API Key这类敏感信息，强制要求
        必须通过环境变量设置。

        Raises:
            ValueError: 如果未设置必要的环境变量（如 ZHIPUAI_API_KEY）。

        Returns:
            LessonPlanGeneratorConfig: 一个包含所有最终配置值的实例。
        """
        zhipuai_api_key = os.getenv("ZHIPUAI_API_KEY")
        if not zhipuai_api_key:
            raise ValueError("请设置环境变量 ZHIPUAI_API_KEY")
        
        # 获取项目根目录（从当前文件位置向上两级）
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        
        # 设置默认路径
        default_output_dir = os.path.join(project_root, "outputs")
        default_log_dir = os.path.join(project_root, "logs")
        
        return cls(
            zhipuai_api_key=zhipuai_api_key,
            zhipuai_base_url=os.getenv("ZHIPUAI_BASE_URL"),
            zhipuai_model=os.getenv("ZHIPUAI_MODEL", "glm-4"),
            output_dir=os.getenv("OUTPUT_DIR", default_output_dir),
            log_dir=os.getenv("LOG_DIR", default_log_dir),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_console=os.getenv("LOG_CONSOLE", "true").lower() == "true"
        ) 