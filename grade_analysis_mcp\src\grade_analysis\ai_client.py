"""Grade Analysis AI Client Module

基于OpenAI兼容接口连接DashScope deepseek-v3模型，实现三个AI代理：
- DataExplorerAgent: 数据结构分析代码生成
- AnalystAgent: 数据分析代码生成  
- ReporterAgent: 报告生成
"""
import time
from typing import Tuple, Optional, AsyncGenerator
from openai import AsyncOpenAI

from .config import GradeAnalysisConfig
from .models import ModelUsageInfo
from .logger import get_logger

logger = get_logger(__name__)


class DashScopeClient:
    """阿里云DashScope客户端（支持DeepSeek模型）
    
    基于transcript_generator_mcp的实现，适配成绩分析需求：
    - 支持DeepSeek-V3模型
    - 完整的错误处理和重试机制
    - 用量统计和成本计算
    - 支持流式和非流式调用
    """

    def __init__(self, config: GradeAnalysisConfig):
        """初始化DashScope客户端"""
        self.config = config
        self._setup_client()

    def _setup_client(self) -> None:
        """设置DashScope客户端"""
        self.client = AsyncOpenAI(
            api_key=self.config.dashscope_api_key,
            base_url=self.config.dashscope_base_url,
            timeout=self.config.request_timeout
        )
        logger.info(f"DashScope客户端初始化完成 - 模型: {self.config.dashscope_model}")



    async def generate_text(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None
    ) -> Tuple[str, ModelUsageInfo]:
        """生成文本并返回用量信息
        
        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词
            max_tokens: 最大token数，默认使用配置值
            temperature: 生成温度，默认使用配置值
            
        Returns:
            Tuple[str, ModelUsageInfo]: 生成的文本和用量信息
        """
        try:
            start_time = time.time()
            
            # 构建消息
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            response = await self.client.chat.completions.create(
                model=self.config.dashscope_model,
                messages=messages,
                max_tokens=max_tokens or self.config.max_tokens,
                temperature=temperature or self.config.temperature,
                timeout=self.config.request_timeout
            )

            response_time = time.time() - start_time
            content = response.choices[0].message.content

            # 获取用量信息
            usage = response.usage
            input_tokens = usage.prompt_tokens
            output_tokens = usage.completion_tokens

            usage_info = ModelUsageInfo(
                vendor="dashscope",
                model_name=self.config.dashscope_model,
                input_tokens=input_tokens,
                output_tokens=output_tokens
            )

            logger.info(f"文本生成完成 - 输入: {input_tokens} tokens, "
                       f"输出: {output_tokens} tokens, 耗时: {response_time:.2f}s")
            
            return content, usage_info

        except Exception as e:
            logger.error(f"DashScope调用失败: {str(e)}")
            raise

    async def stream_generate_text(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None
    ) -> AsyncGenerator[Tuple[str, Optional[ModelUsageInfo]], None]:
        """流式生成文本
        
        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词
            max_tokens: 最大token数，默认使用配置值
            temperature: 生成温度，默认使用配置值
            
        Yields:
            Tuple[str, Optional[ModelUsageInfo]]: 文本片段和用量信息（最后一次返回）
        """
        try:
            start_time = time.time()
            
            # 构建消息
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            stream = await self.client.chat.completions.create(
                model=self.config.dashscope_model,
                messages=messages,
                max_tokens=max_tokens or self.config.max_tokens,
                temperature=temperature or self.config.temperature,
                stream=True,
                stream_options={"include_usage": True},
                timeout=self.config.request_timeout
            )

            content_chunks = []
            usage_info = None

            async for chunk in stream:
                if not chunk.choices:
                    # 最后一个chunk包含usage信息
                    if chunk.usage:
                        response_time = time.time() - start_time
                        usage = chunk.usage
                        cost_estimate = self._calculate_cost(
                            usage.prompt_tokens, 
                            usage.completion_tokens
                        )

                        usage_info = ModelUsageInfo(
                            vendor="dashscope",
                            model_name=self.config.dashscope_model,
                            input_tokens=usage.prompt_tokens,
                            output_tokens=usage.completion_tokens,
                            cost_estimate=cost_estimate
                        )
                        
                        logger.info(f"流式文本生成完成 - 输入: {usage.prompt_tokens} tokens, "
                                   f"输出: {usage.completion_tokens} tokens, "
                                   f"耗时: {response_time:.2f}s, 成本: ¥{cost_estimate:.4f}")
                else:
                    delta = chunk.choices[0].delta
                    if delta.content:
                        content_chunks.append(delta.content)
                        yield delta.content, None

            # 返回最终的用量信息
            if usage_info:
                yield "", usage_info

        except Exception as e:
            logger.error(f"DashScope流式调用失败: {str(e)}")
            raise


class DataExplorerAgent:
    """数据结构分析代理
    
    负责生成数据探索代码，了解数据的基本结构和特征。
    """
    
    def __init__(self, client: DashScopeClient):
        self.client = client
        
    async def generate_exploration_code(
        self, 
        data_info: str
    ) -> Tuple[str, ModelUsageInfo]:
        """生成数据探索代码
        
        Args:
            data_info: 数据基本信息（列名、形状等）
            
        Returns:
            Tuple[str, ModelUsageInfo]: 生成的Python代码和用量信息
        """
        system_prompt = """你是一个专业的数据分析师。你的任务是生成Python代码来探索和了解数据的基本结构。

要求：
1. 生成安全、高效的Python代码
2. 使用pandas库进行数据分析
3. 代码应该包含数据基本信息的获取
4. 输出结果应该是结构化的，便于后续处理
5. 代码中不要包含任何危险操作（如文件删除、网络访问等）

请只返回Python代码，不要包含其他解释文字。"""

        prompt = f"""请为以下数据生成探索代码：

数据信息：
{data_info}

请生成Python代码来：
1. 加载数据（数据文件路径为 '/app/data/cleaned_grade_data.csv'，在Docker容器中运行）
2. 显示数据的基本信息（形状、列名、数据类型）
3. 显示数据的前几行
4. 检查缺失值情况
5. 对数值列进行基本统计描述

代码应该将结果打印出来，便于捕获输出。"""

        return await self.client.generate_text(prompt, system_prompt)


class AnalystAgent:
    """数据分析代理
    
    负责根据用户需求生成针对性的数据分析代码。
    """
    
    def __init__(self, client: DashScopeClient):
        self.client = client
        
    async def generate_analysis_code(
        self, 
        user_request: str, 
        data_structure: str
    ) -> Tuple[str, ModelUsageInfo]:
        """生成数据分析代码
        
        Args:
            user_request: 用户的分析需求
            data_structure: 数据结构信息
            
        Returns:
            Tuple[str, ModelUsageInfo]: 生成的Python代码和用量信息
        """
        system_prompt = """你是一个专业的数据分析师。你的任务是根据用户需求生成Python代码来分析成绩数据。

要求：
1. 生成安全、高效的Python代码
2. 使用pandas、numpy等常用数据分析库
3. 代码应该直接回答用户的问题
4. 输出结果应该是结构化的，便于生成报告
5. 代码中不要包含任何危险操作
6. 处理可能的数据问题（如缺失值、数据类型等）

请只返回Python代码，不要包含其他解释文字。"""

        prompt = f"""用户需求：
{user_request}

数据结构信息：
{data_structure}

请生成Python代码来：
1. 加载数据（数据文件路径为 '/app/data/cleaned_grade_data.csv'，在Docker容器中运行）
2. 根据用户需求进行相应的数据分析
3. 将分析结果打印出来，格式清晰易读
4. 如果涉及计算，请确保结果准确

代码应该能够完整回答用户的问题。"""

        return await self.client.generate_text(prompt, system_prompt)


class ReporterAgent:
    """报告生成代理
    
    负责基于分析结果生成用户友好的分析报告。
    """
    
    def __init__(self, client: DashScopeClient):
        self.client = client
        
    async def generate_report(
        self, 
        user_request: str, 
        analysis_results: str
    ) -> Tuple[str, ModelUsageInfo]:
        """生成分析报告
        
        Args:
            user_request: 用户的原始需求
            analysis_results: 数据分析的结果
            
        Returns:
            Tuple[str, ModelUsageInfo]: 生成的Markdown格式报告和用量信息
        """
        system_prompt = """你是一个专业的数据分析报告撰写专家。你的任务是基于数据分析结果，撰写一份清晰、专业的分析报告。

要求：
1. 使用Markdown格式撰写报告
2. 报告应该结构清晰，包含标题、摘要、详细分析、结论等部分
3. 语言通俗易懂，适合教师和学生阅读
4. 突出关键发现和洞察
5. 如果有数值结果，请用表格或列表清晰展示
6. 提供有价值的建议和改进方向
7. 不要添加附录、数据说明等额外部分，专注于分析结果本身

请只返回Markdown格式的报告内容。"""

        prompt = f"""用户原始需求：
{user_request}

数据分析结果：
{analysis_results}

请基于以上信息生成一份完整的成绩分析报告，报告应该：
1. 有清晰的标题和结构
2. 总结关键发现
3. 详细解读分析结果
4. 提供有价值的建议
5. 使用Markdown格式，便于阅读
6. 不要包含附录、数据说明等额外内容

报告应该专业且易于理解，专注于分析结果和洞察。"""

        return await self.client.generate_text(prompt, system_prompt)
