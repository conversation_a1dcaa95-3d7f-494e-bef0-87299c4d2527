"""Grade Analysis MCP Server Main Entry Point

基于FastMCP框架的成绩分析MCP服务器主入口文件。
提供analyze_grades工具函数，支持Markdown表格数据的智能分析。
"""
import uuid
from typing import Dict, Any
from fastmcp import FastMCP, Context

from src.grade_analysis.config import GradeAnalysisConfig
from src.grade_analysis.models import AnalysisRequest
from src.grade_analysis.analyzer import GradeAnalyzer
from src.grade_analysis.logger import get_logger

# 初始化日志
logger = get_logger(__name__)

# 初始化配置
try:
    config = GradeAnalysisConfig.from_env()
    logger.info("配置加载成功")
except Exception as e:
    logger.error(f"配置加载失败: {str(e)}")
    raise

# 初始化分析器
try:
    analyzer = GradeAnalyzer(config)
    logger.info("成绩分析器初始化成功")
except Exception as e:
    logger.error(f"成绩分析器初始化失败: {str(e)}")
    raise

# 创建FastMCP应用
mcp = FastMCP("Grade Analysis MCP Server")


@mcp.tool()
async def analyze_grades(
    data_string: str,
    user_request: str,
    analysis_id: str = None,
    ctx: Context = None
) -> Dict[str, Any]:
    """分析学生成绩数据

    这个工具可以：
    1. 解析Markdown格式的成绩表格
    2. 根据用户需求进行数据分析
    3. 生成Python分析代码并安全执行
    4. 输出详细的分析报告

    Args:
        data_string: Markdown格式的成绩表格数据
        user_request: 用户的分析需求描述
        analysis_id: 可选的分析任务ID，如果不提供会自动生成
        ctx: FastMCP上下文对象（自动注入）

    Returns:
        Dict[str, Any]: 包含分析结果的字典
    """
    if analysis_id is None:
        analysis_id = str(uuid.uuid4())

    logger.info(f"开始分析任务: {analysis_id}")
    logger.info(f"用户需求: {user_request}")

    try:
        # 创建分析请求
        request = AnalysisRequest(
            analysis_id=analysis_id,
            data_string=data_string,
            user_request=user_request
        )

        # 执行分析
        result = await analyzer.analyze_grades(request, ctx)

        # 返回结果
        metadata = result.metadata
        data_processing_info = metadata.get("data_processing_info", {})
        model_usage = metadata.get("model_usage", [])

        return {
            "success": result.success,
            "analysis_id": result.analysis_id,
            "user_request": metadata.get("user_request", ""),
            "data_info": {
                "rows_count": data_processing_info.get("rows_count", 0),
                "columns_count": data_processing_info.get("columns_count", 0),
                "columns_names": data_processing_info.get("columns_names", []),
                "processing_time": data_processing_info.get("processing_time", 0.0)
            },
            "report_content": result.report,
            "analysis_folder": metadata.get("analysis_folder", ""),
            "total_time": metadata.get("total_time", 0.0),
            "model_usage_summary": {
                "total_calls": len(model_usage),
                "total_input_tokens": sum(usage.get("input_tokens", 0) for usage in model_usage),
                "total_output_tokens": sum(usage.get("output_tokens", 0) for usage in model_usage)
            },
            "files": result.files
        }

    except Exception as e:
        error_msg = f"分析失败: {str(e)}"
        logger.error(error_msg, exc_info=True)

        # 发送错误信息到客户端
        if ctx:
            await ctx.error({
                "type": "analysis_error",
                "message": error_msg,
                "analysis_id": analysis_id
            })

        return {
            "success": False,
            "error": error_msg,
            "analysis_id": analysis_id
        }


@mcp.tool()
async def test_environment(ctx: Context = None) -> Dict[str, Any]:
    """测试分析环境的可用性"""
    logger.info("开始环境测试")

    try:
        if ctx:
            await ctx.info({
                "status": "test_started",
                "message": "开始测试分析环境"
            })

        # 执行组件测试
        test_results = await analyzer.test_components(ctx)

        # 计算总体状态
        all_passed = all(test_results.values())

        result = {
            "success": all_passed,
            "components": test_results,
            "message": "所有组件测试通过" if all_passed else "部分组件测试失败"
        }

        logger.info(f"环境测试完成: {result['message']}")
        return result

    except Exception as e:
        error_msg = f"环境测试失败: {str(e)}"
        logger.error(error_msg, exc_info=True)

        return {
            "success": False,
            "error": error_msg
        }


if __name__ == "__main__":
    # 启动MCP服务器
    logger.info("启动Grade Analysis MCP Server")
    # 运行MCP服务器（HTTP流模式）
    mcp.run(transport='streamable-http')


