"""Grade Analysis Safe Code Executor Module

实现基于Docker的安全Python代码执行环境，包括资源限制和权限控制。
"""
import os
import time
import tempfile
import json
from typing import Dict, Any, Optional
import docker
from docker.errors import ContainerError, ImageNotFound, APIError

from .config import GradeAnalysisConfig
from .models import CodeExecutionResult
from .logger import get_logger

logger = get_logger(__name__)


class SafeCodeExecutor:
    """安全代码执行器
    
    使用Docker容器提供完全隔离的Python代码执行环境，
    包括严格的资源限制和安全控制。
    """
    
    def __init__(self, config: GradeAnalysisConfig):
        """初始化安全代码执行器
        
        Args:
            config: 应用配置对象
        """
        self.config = config
        self.docker_client = None
        self._setup_docker_client()
        
    def _setup_docker_client(self) -> None:
        """设置Docker客户端"""
        try:
            self.docker_client = docker.from_env()
            # 测试Docker连接
            self.docker_client.ping()
            logger.info("Docker客户端初始化成功")
            
            # 检查镜像是否存在
            self._ensure_image_available()
            
        except Exception as e:
            logger.error(f"Docker客户端初始化失败: {str(e)}")
            raise RuntimeError(f"Docker环境不可用: {str(e)}")
    
    def _ensure_image_available(self) -> None:
        """确保Docker镜像可用"""
        try:
            self.docker_client.images.get(self.config.docker_image)
            logger.info(f"Docker镜像 {self.config.docker_image} 已存在")
        except ImageNotFound:
            logger.info(f"正在拉取Docker镜像: {self.config.docker_image}")
            try:
                self.docker_client.images.pull(self.config.docker_image)
                logger.info(f"Docker镜像 {self.config.docker_image} 拉取成功")
            except Exception as e:
                logger.error(f"拉取Docker镜像失败: {str(e)}")
                raise

    def _clean_code_response(self, code_text: str) -> str:
        """清理AI生成的代码，移除markdown标记

        Args:
            code_text: AI生成的原始代码文本

        Returns:
            str: 清理后的纯Python代码
        """
        # 移除开头的```python或```
        if code_text.strip().startswith('```python'):
            code_text = code_text.strip()[9:]
        elif code_text.strip().startswith('```'):
            code_text = code_text.strip()[3:]

        # 移除结尾的```
        if code_text.strip().endswith('```'):
            code_text = code_text.strip()[:-3]

        return code_text.strip()

    def _create_safe_code(self, code: str, data_path: str) -> str:
        """创建安全的执行代码

        Args:
            code: 原始Python代码（可能包含markdown标记）
            data_path: 数据文件路径

        Returns:
            str: 包装后的安全代码
        """
        # 首先清理代码，移除markdown标记
        code = self._clean_code_response(code)
        # 代码安全检查
        dangerous_keywords = [
            'import os', 'import sys', 'import subprocess', 'import socket',
            'import urllib', 'import requests', 'import httpx',
            'open(', 'file(', 'exec(', 'eval(', '__import__',
            'compile(', 'globals()', 'locals()', 'vars()',
            'delattr', 'setattr', 'getattr', 'hasattr'
        ]
        
        code_lower = code.lower()
        for keyword in dangerous_keywords:
            if keyword in code_lower:
                logger.warning(f"检测到潜在危险代码: {keyword}")
                # 这里可以选择拒绝执行或者进行更严格的沙箱化
        
        # 修复用户代码中的数据文件路径
        # 将相对路径替换为Docker容器内的绝对路径
        # 注意：按照从具体到一般的顺序替换，避免重复替换
        code = code.replace("'data/cleaned_grade_data.csv'", "'/app/data/cleaned_grade_data.csv'")
        code = code.replace('"data/cleaned_grade_data.csv"', '"/app/data/cleaned_grade_data.csv"')
        # 只替换没有被引号包围的相对路径（避免重复替换已经处理过的路径）
        import re
        # 使用正则表达式，只替换不在 /app/ 前缀后面的 data/cleaned_grade_data.csv
        code = re.sub(r'(?<!/app/)data/cleaned_grade_data\.csv', '/app/data/cleaned_grade_data.csv', code)

        # 包装代码，添加结果捕获
        # 为用户代码添加正确的缩进
        indented_code = '\n'.join('    ' + line for line in code.split('\n'))

        safe_code = f"""
import sys
import json
import subprocess
import traceback
import os
from io import StringIO

# 安装必要的包
try:
    import pandas as pd
    import numpy as np
except ImportError:
    print("正在安装必要的Python包...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pandas", "numpy", "tabulate"])
    import pandas as pd
    import numpy as np
    print("包安装完成")

# 设置数据文件路径映射
# 在Docker容器中，数据文件夹被映射到 /app/data
DATA_DIR = "/app/data"
print(f"数据目录: {{DATA_DIR}}")

# 检查数据目录是否存在
if os.path.exists(DATA_DIR):
    print(f"数据目录存在，包含文件: {{os.listdir(DATA_DIR)}}")
else:
    print(f"警告: 数据目录不存在: {{DATA_DIR}}")

# 重定向输出
old_stdout = sys.stdout
sys.stdout = captured_output = StringIO()

try:
    # 用户代码开始
{indented_code}
    # 用户代码结束
    
    # 捕获输出
    output = captured_output.getvalue()
    result = {{
        "success": True,
        "output": output,
        "error": None
    }}
    
except Exception as e:
    # 捕获错误
    error_info = {{
        "type": type(e).__name__,
        "message": str(e),
        "traceback": traceback.format_exc()
    }}
    result = {{
        "success": False,
        "output": captured_output.getvalue(),
        "error": error_info
    }}

finally:
    # 恢复输出
    sys.stdout = old_stdout
    
    # 输出结果（JSON格式）
    print("===RESULT_START===")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print("===RESULT_END===")
"""
        return safe_code
    
    def _save_code_file(self, code: str, data_folder: str, code_file_path: str) -> str:
        """保存代码文件到指定路径

        Args:
            code: 要执行的Python代码
            data_folder: 数据文件夹路径
            code_file_path: 代码文件保存路径

        Returns:
            str: 保存的文件路径
        """
        # 确保目录存在
        os.makedirs(os.path.dirname(code_file_path), exist_ok=True)

        # 创建安全代码并保存
        safe_code = self._create_safe_code(code, data_folder)
        with open(code_file_path, 'w', encoding='utf-8') as f:
            f.write(safe_code)

        logger.info(f"代码已保存到: {code_file_path}")
        return code_file_path
    
    def _parse_execution_result(self, output: str) -> Dict[str, Any]:
        """解析执行结果
        
        Args:
            output: 容器输出
            
        Returns:
            Dict[str, Any]: 解析后的结果
        """
        try:
            # 查找结果标记
            start_marker = "===RESULT_START==="
            end_marker = "===RESULT_END==="
            
            start_idx = output.find(start_marker)
            end_idx = output.find(end_marker)
            
            if start_idx == -1 or end_idx == -1:
                logger.warning("未找到结果标记，使用原始输出")
                return {
                    "success": False,
                    "output": output,
                    "error": {"message": "无法解析执行结果"}
                }
            
            # 提取JSON结果
            json_str = output[start_idx + len(start_marker):end_idx].strip()
            result = json.loads(json_str)
            
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"解析执行结果失败: {str(e)}")
            return {
                "success": False,
                "output": output,
                "error": {"message": f"JSON解析失败: {str(e)}"}
            }
        except Exception as e:
            logger.error(f"处理执行结果时发生错误: {str(e)}")
            return {
                "success": False,
                "output": output,
                "error": {"message": f"结果处理失败: {str(e)}"}
            }
    
    async def execute_code_file(
        self,
        code_file_path: str,
        data_folder: str,
        timeout: Optional[int] = None
    ) -> CodeExecutionResult:
        """执行Python代码文件

        Args:
            code_file_path: 要执行的Python代码文件路径
            data_folder: 数据文件夹路径
            timeout: 执行超时时间（秒）

        Returns:
            CodeExecutionResult: 执行结果
        """
        start_time = time.time()
        container = None

        try:
            # 检查代码文件是否存在
            if not os.path.exists(code_file_path):
                raise FileNotFoundError(f"代码文件不存在: {code_file_path}")
            
            # 设置容器参数
            timeout = timeout or self.config.execution_timeout

            # 容器配置 - 确保使用绝对路径
            abs_code_file = os.path.abspath(code_file_path)
            abs_data_folder = os.path.abspath(data_folder)

            container_config = {
                'image': self.config.docker_image,
                'command': ['python', '/app/code.py'],
                'volumes': {
                    abs_code_file: {'bind': '/app/code.py', 'mode': 'ro'},
                    abs_data_folder: {'bind': '/app/data', 'mode': 'ro'}
                },
                'working_dir': '/app',
                'mem_limit': self.config.max_memory,
                'cpu_quota': int(self.config.max_cpu * 100000),  # CPU限制
                'cpu_period': 100000,
                'network_disabled': True,  # 禁用网络
                'read_only': True,  # 只读文件系统
                'tmpfs': {'/tmp': 'size=100m'},  # 临时文件系统
                'security_opt': ['no-new-privileges'],  # 安全选项
                'user': 'nobody',  # 非特权用户
                'remove': False  # 不自动删除容器，手动删除
            }

            logger.info(f"开始执行代码，超时时间: {timeout}秒")

            # 运行容器
            container = self.docker_client.containers.run(
                **container_config,
                detach=True
            )

            try:
                # 等待容器完成
                result = container.wait(timeout=timeout)
                return_code = result['StatusCode']

                # 获取输出
                logs = container.logs(stdout=True, stderr=True).decode('utf-8')
            finally:
                # 确保容器被删除
                try:
                    container.remove(force=True)
                except Exception as e:
                    logger.warning(f"删除容器失败: {str(e)}")
            
            execution_time = time.time() - start_time
            
            # 解析结果
            if return_code == 0:
                parsed_result = self._parse_execution_result(logs)
                
                execution_result = CodeExecutionResult(
                    success=parsed_result.get('success', False),
                    stdout=parsed_result.get('output', ''),
                    stderr='',
                    execution_time=execution_time,
                    return_code=return_code,
                    results=parsed_result
                )
                
                if parsed_result.get('success'):
                    logger.info(f"代码执行成功，耗时: {execution_time:.2f}秒")
                else:
                    logger.warning(f"代码执行有错误: {parsed_result.get('error', {}).get('message', '未知错误')}")
                    
            else:
                logger.error(f"容器执行失败，返回码: {return_code}")
                execution_result = CodeExecutionResult(
                    success=False,
                    stdout='',
                    stderr=logs,
                    execution_time=execution_time,
                    return_code=return_code,
                    results=None
                )
            
            return execution_result
            
        except docker.errors.ContainerError as e:
            execution_time = time.time() - start_time
            logger.error(f"容器执行错误: {str(e)}")
            return CodeExecutionResult(
                success=False,
                stdout='',
                stderr=str(e),
                execution_time=execution_time,
                return_code=e.exit_status,
                results=None
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"代码执行失败: {str(e)}")
            return CodeExecutionResult(
                success=False,
                stdout='',
                stderr=str(e),
                execution_time=execution_time,
                return_code=-1,
                results=None
            )
            
        finally:
            # 容器清理已在内层try-finally中处理
            pass
    
    async def test_environment(self) -> bool:
        """测试执行环境

        Returns:
            bool: 环境是否正常
        """
        try:
            test_code = """
import pandas as pd
import numpy as np
print("环境测试成功")
print(f"pandas版本: {pd.__version__}")
print(f"numpy版本: {np.__version__}")
"""

            # 创建临时数据文件夹
            with tempfile.TemporaryDirectory() as temp_dir:
                # 创建测试CSV文件
                test_csv = os.path.join(temp_dir, 'test.csv')
                with open(test_csv, 'w', encoding='utf-8') as f:
                    f.write("name,score\ntest,100\n")

                # 执行测试代码
                result = await self.execute_code(test_code, temp_dir, timeout=10)

                if result.success:
                    logger.info("代码执行环境测试通过")
                    return True
                else:
                    logger.error(f"代码执行环境测试失败: {result.stderr}")
                    return False

        except Exception as e:
            logger.error(f"环境测试失败: {str(e)}")
            return False
