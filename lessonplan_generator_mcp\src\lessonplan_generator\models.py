"""AI教案生成服务器数据模型"""
from typing import List, Optional
from pydantic import BaseModel, Field


class LessonPlanInfo(BaseModel):
    """封装用户请求的教案基本信息"""
    topic: str = Field(description="教案主题")
    grade: str = Field(description="授课年级")
    subject: str = Field(description="所属学科")
    teaching_hours: int = Field(default=1, description="计划课时")
    user_requirements: Optional[str] = Field(
        default="", 
        description="用户提出的其他具体或特殊要求"
    )


class ModelUsageInfo(BaseModel):
    """记录单次模型调用的用量信息"""
    vendor: str = Field(description="提供模型的厂商，例如：doubao, zhipu")
    model_name: str = Field(description="本次调用的具体模型名称")
    input_tokens: int = Field(description="输入给模型的Token数量")
    output_tokens: int = Field(description="模型生成的Token数量") 