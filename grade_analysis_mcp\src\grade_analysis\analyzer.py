"""Grade Analysis Core Service Module

实现核心的成绩分析服务，整合数据处理、AI分析和代码执行功能。
"""
import asyncio
import time
import os
import shutil
import json
from datetime import datetime
from typing import Dict, Any, Optional
from fastmcp import Context

from .config import GradeAnalysisConfig
from .models import (
    AnalysisRequest, AnalysisResult, ProgressUpdate, 
    ModelUsageInfo, CodeExecutionResult
)
from .data_processor import DataProcessor
from .ai_client import DashScopeClient, DataExplorerAgent, AnalystAgent, ReporterAgent
from .code_executor import SafeCodeExecutor
from .logger import get_logger

logger = get_logger(__name__)


def clean_code_response(code_text: str) -> str:
    """清理AI生成的代码，移除markdown标记

    Args:
        code_text: AI生成的原始代码文本

    Returns:
        str: 清理后的纯Python代码
    """
    # 移除开头的```python或```
    if code_text.strip().startswith('```python'):
        code_text = code_text.strip()[9:]
    elif code_text.strip().startswith('```'):
        code_text = code_text.strip()[3:]

    # 移除结尾的```
    if code_text.strip().endswith('```'):
        code_text = code_text.strip()[:-3]

    return code_text.strip()


class GradeAnalyzer:
    """成绩分析器主服务类
    
    整合所有功能模块，提供完整的成绩分析服务：
    1. 数据预处理（Markdown表格 -> CSV）
    2. 数据探索（生成并执行探索代码）
    3. 针对性分析（根据用户需求生成分析代码）
    4. 报告生成（基于分析结果生成Markdown报告）
    """
    
    def __init__(self, config: GradeAnalysisConfig):
        """初始化分析器
        
        Args:
            config: 应用配置对象
        """
        self.config = config
        
        # 初始化各个组件
        # 使用项目内部输出目录进行数据处理
        self.data_processor = DataProcessor(config.output_dir)
        self.dashscope_client = DashScopeClient(config)
        self.data_explorer = DataExplorerAgent(self.dashscope_client)
        self.analyst = AnalystAgent(self.dashscope_client)
        self.reporter = ReporterAgent(self.dashscope_client)
        
        # 初始化代码执行器（如果启用Docker）
        self.code_executor = None
        if config.enable_docker:
            try:
                self.code_executor = SafeCodeExecutor(config)
                logger.info("安全代码执行器初始化成功")
            except Exception as e:
                logger.warning(f"安全代码执行器初始化失败，将跳过代码执行: {str(e)}")
        else:
            logger.info("Docker执行已禁用，将跳过代码执行")
    
    async def _send_progress(self, ctx: Context, status: str, message: str = "", **kwargs) -> None:
        """发送进度更新

        Args:
            ctx: FastMCP上下文对象
            status: 状态标识
            message: 状态描述信息
            **kwargs: 额外的状态信息
        """
        from datetime import datetime
        progress = ProgressUpdate(
            status=status,
            message=message,
            timestamp=datetime.now().isoformat(),
            **kwargs
        )
        await ctx.info(progress.model_dump())
        logger.info(f"进度更新: {status} - {message}")
    
    async def _send_usage_info(self, ctx: Context, usage_info: ModelUsageInfo) -> None:
        """发送模型用量信息
        
        Args:
            ctx: FastMCP上下文对象
            usage_info: 模型用量信息
        """
        await ctx.info({
            "status": "model_usage",
            "usage": usage_info.model_dump()
        })
        logger.info(f"模型用量: {usage_info.vendor} {usage_info.model_name} - "
                   f"输入: {usage_info.input_tokens}, 输出: {usage_info.output_tokens}")
    
    async def analyze_grades(
        self, 
        request: AnalysisRequest, 
        ctx: Context
    ) -> AnalysisResult:
        """执行完整的成绩分析流程
        
        Args:
            request: 分析请求对象
            ctx: FastMCP上下文对象
            
        Returns:
            AnalysisResult: 分析结果对象
        """
        start_time = time.time()
        total_usage = []
        
        try:
            await self._send_progress(ctx, "started", message="成绩分析任务开始")
            
            # 第一步：数据预处理
            await self._send_progress(ctx, "data_processing", message="正在处理数据...")
            
            cleaned_data, processing_info, analysis_folder = self.data_processor.process_data(
                request.data_string,
                request.analysis_id
            )
            
            await self._send_progress(
                ctx,
                "data_processed",
                f"数据处理完成，共{processing_info.rows_count}行{processing_info.columns_count}列",
                details={"data_info": processing_info.model_dump()}
            )
            
            # 第二步：数据探索
            await self._send_progress(ctx, "data_exploration", message="正在生成数据探索代码...")
            
            data_info_str = f"""
数据形状: {processing_info.rows_count}行 x {processing_info.columns_count}列
列名: {', '.join(processing_info.columns_names)}
数据类型: CSV格式
文件路径: data/cleaned_grade_data.csv
"""
            
            exploration_code, exploration_usage = await self.data_explorer.generate_exploration_code(
                data_info_str
            )
            total_usage.append(exploration_usage)
            await self._send_usage_info(ctx, exploration_usage)

            # 保存探索代码到code目录
            exploration_code_path = os.path.join(analysis_folder, "code", "exploration_code.py")
            self.code_executor._save_code_file(
                exploration_code,
                os.path.join(analysis_folder, "data"),
                exploration_code_path
            )

            logger.info(f"数据探索代码已保存: {exploration_code_path}")

            # 执行数据探索代码
            exploration_result = None
            if self.code_executor:
                await self._send_progress(ctx, "code_execution", message="正在执行数据探索代码...")

                # 传递数据文件夹路径而不是整个分析文件夹
                data_folder = os.path.join(analysis_folder, "data")
                exploration_result = await self.code_executor.execute_code_file(
                    exploration_code_path,
                    data_folder
                )
                
                if exploration_result.success:
                    await self._send_progress(
                        ctx,
                        "exploration_completed",
                        "数据探索完成",
                        details={"output": exploration_result.stdout[:500] + "..." if len(exploration_result.stdout) > 500 else exploration_result.stdout}
                    )
                else:
                    await self._send_progress(
                        ctx,
                        "exploration_failed",
                        f"数据探索失败: {exploration_result.stderr}"
                    )
            
            # 第三步：针对性分析
            await self._send_progress(ctx, "analysis_generation", message="正在生成分析代码...")
            
            # 构建数据结构信息（包含探索结果）
            data_structure = data_info_str
            if exploration_result and exploration_result.success:
                data_structure += f"\n\n数据探索结果:\n{exploration_result.stdout}"
            
            analysis_code, analysis_usage = await self.analyst.generate_analysis_code(
                request.user_request,
                data_structure
            )
            total_usage.append(analysis_usage)
            await self._send_usage_info(ctx, analysis_usage)

            # 保存分析代码到code目录
            analysis_code_path = os.path.join(analysis_folder, "code", "analysis_code.py")
            self.code_executor._save_code_file(
                analysis_code,
                os.path.join(analysis_folder, "data"),
                analysis_code_path
            )

            logger.info(f"数据分析代码已保存: {analysis_code_path}")

            # 执行分析代码
            analysis_result = None
            if self.code_executor:
                await self._send_progress(ctx, "analysis_execution", message="正在执行分析代码...")

                # 传递数据文件夹路径而不是整个分析文件夹
                data_folder = os.path.join(analysis_folder, "data")
                analysis_result = await self.code_executor.execute_code_file(
                    analysis_code_path,
                    data_folder
                )
                
                if analysis_result.success:
                    await self._send_progress(
                        ctx,
                        "analysis_completed",
                        "数据分析完成",
                        details={"output": analysis_result.stdout[:500] + "..." if len(analysis_result.stdout) > 500 else analysis_result.stdout}
                    )
                else:
                    await self._send_progress(
                        ctx,
                        "analysis_failed",
                        f"数据分析失败: {analysis_result.stderr}"
                    )
            
            # 第四步：生成报告
            await self._send_progress(ctx, "report_generation", message="正在生成分析报告...")
            
            # 准备分析结果文本
            analysis_results_text = ""
            if exploration_result and exploration_result.success:
                analysis_results_text += f"数据探索结果:\n{exploration_result.stdout}\n\n"
            
            if analysis_result and analysis_result.success:
                analysis_results_text += f"分析结果:\n{analysis_result.stdout}"
            else:
                analysis_results_text += "分析代码执行失败，无法获取具体结果。"
            
            report_content, report_usage = await self.reporter.generate_report(
                request.user_request,
                analysis_results_text
            )
            total_usage.append(report_usage)
            await self._send_usage_info(ctx, report_usage)


            # 保存报告（清理多余的markdown代码块标记）
            cleaned_report = self._clean_markdown_content(report_content)
            report_path = os.path.join(analysis_folder, "result", "analysis_report.md")
            os.makedirs(os.path.dirname(report_path), exist_ok=True)
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_report)
            
            # 计算总耗时
            total_time = time.time() - start_time

            # 保存分析元数据
            metadata_path = self._save_analysis_metadata(
                analysis_folder,
                request,
                processing_info,
                exploration_code,
                analysis_code,
                cleaned_report,
                total_time,
                total_usage
            )

            # 构建文件列表
            files = [
                processing_info.file_path,
                report_path,
                metadata_path,
                exploration_code_path,
                analysis_code_path
            ]

            # 构建最终结果
            result = AnalysisResult(
                success=True,
                analysis_id=request.analysis_id,
                report=report_content or "报告生成失败",
                metadata={
                    "user_request": request.user_request,
                    "data_processing_info": processing_info.model_dump(),
                    "exploration_code": exploration_code,
                    "analysis_code": analysis_code,
                    "analysis_folder": analysis_folder,
                    "total_time": total_time,
                    "model_usage": [usage.model_dump() for usage in total_usage],
                    "execution_results": {
                        "exploration": exploration_result.model_dump() if exploration_result else None,
                        "analysis": analysis_result.model_dump() if analysis_result else None
                    }
                },
                files=files
            )
            


            await self._send_progress(
                ctx,
                "completed",
                f"分析完成，总耗时: {total_time:.2f}秒",
                details={
                    "analysis_folder": analysis_folder,
                    "report_path": report_path
                }
            )

            logger.info(f"成绩分析完成: {request.analysis_id}, 耗时: {total_time:.2f}秒")
            logger.info(f"结果已保存到: {analysis_folder}")
            return result
            
        except Exception as e:
            error_msg = f"分析过程中发生错误: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            await ctx.error({
                "type": "analysis_error",
                "message": error_msg,
                "analysis_id": request.analysis_id
            })
            
            raise
    
    async def test_components(self, ctx: Context) -> Dict[str, bool]:
        """测试各个组件的可用性
        
        Args:
            ctx: FastMCP上下文对象
            
        Returns:
            Dict[str, bool]: 各组件的测试结果
        """
        results = {}
        
        # 测试数据处理器
        try:
            test_data = "| 姓名 | 数学 | 语文 |\n|------|------|------|\n| 张三 | 85 | 90 |\n| 李四 | 78 | 88 |"
            df, _, _ = self.data_processor.process_data(test_data)
            results["data_processor"] = len(df) == 2
            await self._send_progress(ctx, "test_data_processor", "数据处理器测试完成", details={"success": results["data_processor"]})
        except Exception as e:
            results["data_processor"] = False
            logger.error(f"数据处理器测试失败: {str(e)}")
        
        # 测试AI客户端
        try:
            test_response, _ = await self.dashscope_client.generate_text("测试连接", max_tokens=10)
            results["ai_client"] = bool(test_response)
            await self._send_progress(ctx, "test_ai_client", "AI客户端测试完成", details={"success": results["ai_client"]})
        except Exception as e:
            results["ai_client"] = False
            logger.error(f"AI客户端测试失败: {str(e)}")
        
        # 测试代码执行器
        if self.code_executor:
            try:
                results["code_executor"] = await self.code_executor.test_environment()
                await self._send_progress(ctx, "test_code_executor", "代码执行器测试完成", details={"success": results["code_executor"]})
            except Exception as e:
                results["code_executor"] = False
                logger.error(f"代码执行器测试失败: {str(e)}")
        else:
            results["code_executor"] = False
            await self._send_progress(ctx, "test_code_executor", "Docker未启用", details={"success": False})
        
        return results


    def _clean_markdown_content(self, content: str) -> str:
        """清理markdown内容，去除多余的代码块标记

        Args:
            content: 原始markdown内容

        Returns:
            str: 清理后的markdown内容
        """
        # 去除开头的markdown代码块标记
        content = content.strip()

        # 如果开头是```markdown，则去除
        if content.startswith('```markdown'):
            content = content[11:].strip()

        # 如果开头是```，则去除
        if content.startswith('```'):
            content = content[3:].strip()

        # 如果结尾是```，则去除
        if content.endswith('```'):
            content = content[:-3].strip()

        return content

    def _save_analysis_metadata(
        self,
        analysis_folder: str,
        request: AnalysisRequest,
        processing_info: Any,
        exploration_code: str,
        analysis_code: str,
        report_content: str,
        total_time: float,
        total_usage: list
    ) -> str:
        """保存分析任务的元数据

        参考picturebook_generator_mcp项目的元数据保存格式，
        保存完整的分析任务元数据信息。

        Args:
            analysis_folder: 分析工作目录
            request: 原始分析请求
            processing_info: 数据处理信息
            exploration_code: 数据探索代码
            analysis_code: 分析代码
            report_content: 生成的报告内容
            total_time: 总耗时
            total_usage: 模型用量信息列表

        Returns:
            str: 元数据文件路径
        """
        metadata = {
            "request_info": {
                "analysis_id": request.analysis_id,
                "user_request": request.user_request,
                "data_length": len(request.data_string),
                "data_preview": request.data_string[:200] + "..." if len(request.data_string) > 200 else request.data_string
            },
            "analysis_info": {
                "timestamp": datetime.now().isoformat(),
                "total_time": total_time,
                "data_processing": processing_info.model_dump() if hasattr(processing_info, 'model_dump') else str(processing_info),
                "report_length": len(report_content)
            },
            "generated_code": {
                "exploration_code": exploration_code,
                "analysis_code": analysis_code
            },
            "model_usage": [usage.model_dump() if hasattr(usage, 'model_dump') else usage for usage in total_usage],
            "files_generated": {
                "analysis_report": "result/analysis_report.md",
                "cleaned_data": "data/cleaned_grade_data.csv",
                "exploration_code": "code/exploration_code.py",
                "analysis_code": "code/analysis_code.py"
            },
            "report_content": report_content
        }

        metadata_path = os.path.join(analysis_folder, "result", "metadata.json")
        os.makedirs(os.path.dirname(metadata_path), exist_ok=True)
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

        logger.info(f"分析元数据已保存至: {metadata_path}")
        return metadata_path
