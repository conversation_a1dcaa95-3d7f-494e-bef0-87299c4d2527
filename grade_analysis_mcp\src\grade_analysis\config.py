"""Grade Analysis MCP Server Configuration Module"""
import os
from typing import Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# 在应用启动时加载.env文件中的环境变量
load_dotenv()


class GradeAnalysisConfig(BaseModel):
    """
    应用配置类，集中管理所有可配置项。
    配置值优先从环境变量中读取，若环境变量未设置，则使用代码中定义的默认值。
    """
    
    # --- DashScope AI 服务配置 ---
    dashscope_api_key: str = Field(description="DashScope平台的API访问密钥")
    dashscope_base_url: str = Field(
        default="https://dashscope.aliyuncs.com/compatible-mode/v1",
        description="DashScope OpenAI兼容接口的基地址"
    )
    dashscope_model: str = Field(
        default="deepseek-v3",
        description="用于数据分析和报告生成的语言大模型名称"
    )
    
    # --- 分析参数配置 ---
    max_tokens: int = Field(default=4000, description="模型生成的最大Token数量")
    temperature: float = Field(
        default=0.1, 
        description="模型生成的温度参数，控制输出的随机性"
    )
    request_timeout: int = Field(default=60, description="API请求超时时间（秒）")
    
    # --- 安全执行配置 ---
    docker_image: str = Field(
        default="python:3.11-slim",
        description="用于安全代码执行的Docker镜像"
    )
    execution_timeout: int = Field(default=60, description="代码执行超时时间（秒）")
    max_memory: str = Field(default="512m", description="代码执行最大内存限制")
    max_cpu: float = Field(default=0.5, description="代码执行最大CPU使用率")
    enable_docker: bool = Field(default=True, description="是否启用Docker安全执行")
    max_retries: int = Field(default=3, description="最大重试次数")

    # --- 输出路径配置 ---
    output_dir: str = Field(default="outputs", description="分析结果文件存放的根目录")
    client_output_dir: str = Field(default=".", description="客户端输出目录（当前工作目录）")
    report_format: str = Field(default="markdown", description="报告输出格式")

    # --- 日志系统配置 ---
    log_dir: str = Field(default="logs", description="日志文件的存放目录")
    log_level: str = Field(default="INFO", description="日志记录的最低级别")
    log_console: bool = Field(default=True, description="是否同时将日志输出到控制台")
    
    @classmethod
    def from_env(cls) -> "GradeAnalysisConfig":
        """
        从环境变量构造配置实例。
        
        该方法会逐一检查每个配置项对应的环境变量，如果存在则使用环境变量的值，
        否则使用在类中定义的默认值。对于API Key这类敏感信息，强制要求
        必须通过环境变量设置。

        Raises:
            ValueError: 如果未设置必要的环境变量（如DASHSCOPE_API_KEY）。

        Returns:
            GradeAnalysisConfig: 一个包含所有最终配置值的实例。
        """
        dashscope_api_key = os.getenv("DASHSCOPE_API_KEY")
        if not dashscope_api_key:
            raise ValueError("请设置环境变量 DASHSCOPE_API_KEY")
        
        # 获取项目根目录（从当前文件位置向上三级）
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
        
        # 设置默认路径
        default_output_dir = os.path.join(project_root, "outputs")
        default_log_dir = os.path.join(project_root, "logs")
        # 客户端输出目录默认为当前工作目录
        default_client_output_dir = os.getcwd()
        
        return cls(
            dashscope_api_key=dashscope_api_key,
            dashscope_base_url=os.getenv(
                "DASHSCOPE_BASE_URL",
                "https://dashscope.aliyuncs.com/compatible-mode/v1"
            ),
            dashscope_model=os.getenv(
                "DASHSCOPE_MODEL",
                "deepseek-v3"
            ),
            max_tokens=int(os.getenv("MAX_TOKENS", "4000")),
            temperature=float(os.getenv("TEMPERATURE", "0.1")),
            request_timeout=int(os.getenv("REQUEST_TIMEOUT", "60")),
            docker_image=os.getenv("DOCKER_IMAGE", "python:3.11-slim"),
            execution_timeout=int(os.getenv("EXECUTION_TIMEOUT", "60")),
            max_memory=os.getenv("MAX_MEMORY", "512m"),
            max_cpu=float(os.getenv("MAX_CPU", "0.5")),
            enable_docker=os.getenv("ENABLE_DOCKER", "true").lower() == "true",
            max_retries=int(os.getenv("MAX_RETRIES", "3")),
            output_dir=os.getenv("OUTPUT_DIR", default_output_dir),
            client_output_dir=os.getenv("CLIENT_OUTPUT_DIR", default_client_output_dir),
            report_format=os.getenv("REPORT_FORMAT", "markdown"),
            log_dir=os.getenv("LOG_DIR", default_log_dir),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_console=os.getenv("LOG_CONSOLE", "true").lower() == "true"
        )
